import type { User } from "firebase/auth";
import * as z from "zod";

/**
 * @interface IAuthA
 * @description Authentication data structure containing user credentials and tokens
 * @property {string} firebaseToken - Token used for Firebase authentication
 * @property {string} apiKey - API key for making authenticated requests
 * @property {string} userId - Unique identifier for the authenticated user
 * @property {string} companyId - Unique identifier for the user's organization
 * @property {string} jwt - JSON Web Token for API authentication
 * @property {string} refreshJwt - Token used to refresh the JWT when expired
 * @property {string} authToken - Primary authentication token for the application
 * @property {string} refreshToken - Token used to refresh the authToken when expired
 */
export interface IAuthA {
  /** Firebase authentication token */
  firebaseToken: string;
  /** API key for authentication */
  apiKey: string;
  /** Unique identifier for the user */
  userId: string;
  /** Unique identifier for the user's company */
  companyId: string;
  /** JSON Web Token for authentication */
  jwt: string;
  /** Refresh token for JWT */
  refreshJwt: string;
  /** Authentication token */
  authToken: string;
  /** Token used to refresh authentication */
  refreshToken: string;
}

/**
 * @description Schema for validating login form data using Zod
 * @property {string} email - User's email address (must be valid email format)
 * @property {string} password - User's password (required)
 * @property {('email'|'phone')} [otpChannel] - Optional channel for OTP delivery
 */
const loginFormPayloadSchema = z.object({
  /** User's email address (must be a valid email format) */
  email: z.string().email(),
  /** User's password */
  password: z.string(),
  /** Optional channel for OTP (One-Time Password) verification */
  otpChannel: z.enum(["email", "phone"]).optional(),
});

/**
 * Inferred type from the login form payload schema
 * Provides type-safe login payload structure
 */
export type ILoginFormPayload = z.infer<typeof loginFormPayloadSchema>;

/**
 * @function useAuthStore
 * @description Pinia store for managing authentication state and operations
 * @returns {Object} Store methods and state properties
 *
 * State properties:
 * @property {Ref<boolean>} isAuthLoading - Tracks authentication loading state
 * @property {Ref<'login'|'otp'>} currentPage - Current authentication page
 * @property {Ref<boolean>} isLoading - Indicates ongoing authentication process
 * @property {Ref<string|null>} loginError - Stores authentication error messages
 * @property {Ref<IAuthA|null>} a - Stores authentication data
 * @property {Ref<User|null>} fbUser - Stores Firebase user object
 *
 * Methods:
 * @property {Function} submitLoginForm - Handles initial login form submission
 * @property {Function} submitOtp - Processes OTP verification
 * @property {Function} getAuthStatus - Checks authentication status
 * @property {Function} setAuth - Updates authentication state
 */
export const useAuthStore = defineStore("auth", () => {
  /** Indicates if authentication process is currently loading */
  const isAuthLoading = ref(true);
  /** Tracks current authentication page (login or OTP) */
  const currentPage = ref<"login" | "otp">("login");
  /** Indicates if a login/authentication process is in progress */
  const isLoading = ref(false);
  /** Stores any login-related error messages */
  const loginError = ref<string | null>(null);
  /** Comprehensive login data storage with optional fields */
  const loginData = ref<{
    /** User's email address */
    email: string;
    /** User's password */
    password: string;
    /** Optional OTP channel */
    otpChannel?: "email" | "phone";
    /** Optional OTP code */
    otp?: string;
    /** Authentication token */
    token?: string;
    /** Device identifier */
    deviceId?: string;
    /** Name of the device */
    deviceName?: string;
    /** Type of device */
    deviceType?: string;
    /** Authentication domain */
    domain?: string;
    /** Company identifier */
    companyId?: string;
    /** Subdomain information */
    subdomain?: string;
  } | null>(null);

  /** Storage for authentication data */
  const a = ref<IAuthA | null>();

  /** Reference to Firebase user object */
  const fbUser = ref<User | null>();

  /**
   * @function setAuth
   * @description Updates the authentication state with user data and Firebase user object
   * @param {IAuthA | null} lsAuth - Authentication data to be stored
   * @param {User | null} user - Firebase user object
   * @returns {void}
   */
  const setAuth = (lsAuth: IAuthA | null, user: User | null): void => {
    a.value = lsAuth;
    fbUser.value = user;
    isAuthLoading.value = false;
  };

  /**
   * @function getAuthStatus
   * @async
   * @description Polls the authentication loading state and returns authentication status
   * @returns {Promise<boolean>} Promise that resolves to true if user is authenticated
   */
  const getAuthStatus = (): Promise<boolean> =>
    new Promise((resolve) => {
      const checkAuth = async () => {
        while (isAuthLoading.value) {
          await new Promise((r) => setTimeout(r, 100));
        }
        resolve(!!a.value);
      };
      checkAuth();
    });

  /**
   * @function submitLoginForm
   * @async
   * @description Handles the initial login form submission process
   * @param {ILoginFormPayload} payload - Login credentials and optional OTP channel
   * @returns {Promise<void>}
   * @throws Will set loginError if validation fails or API request errors
   */
  const submitLoginForm = async (payload: ILoginFormPayload): Promise<void> => {
    const parsedPayload = loginFormPayloadSchema.safeParse(payload);
    if (!parsedPayload.success) {
      loginError.value = getFirstZodError(parsedPayload);
      return;
    }
    loginData.value = null;
    loginError.value = null;
    isLoading.value = true;
    const deviceInfo = getDevice();
    loginData.value = {
      ...payload,
      ...deviceInfo,
    };
    const res = await $fetch<LoginResponse>("/login", {
      method: "POST",
      body: loginData.value,
    });
    if (res.error) {
      loginError.value =
        res.message ??
        "Something went wrong, Please try again or contact support if the issue persists.";
      isLoading.value = false;
      return;
    }
    if (!res.error && res.token) {
      loginData.value.token = res.token;
      currentPage.value = "otp";
      isLoading.value = false;
    } else {
      loginError.value =
        res.message ??
        "Something went wrong, Please try again or contact support if the issue persists.";
      isLoading.value = false;
      return;
    }
  };

  /**
   * @function submitOtp
   * @async
   * @description Processes OTP verification and completes the authentication flow
   * @param {string} otpCode - The one-time password code entered by the user
   * @returns {Promise<void>}
   * @throws Will set loginError if validation fails or API request errors
   */
  const submitOtp = async (otpCode: string): Promise<void> => {
    if (!loginData.value) return;
    loginData.value.otp = otpCode;

    const parsedPayload = loginFormPayloadSchema.safeParse(loginData.value);
    if (!parsedPayload.success) {
      loginError.value = getFirstZodError(parsedPayload);
      return;
    }

    const res = await $fetch<OtpResponse>("/login", {
      method: "POST",
      body: loginData.value,
    });

    if (res.error) {
      loginError.value =
        res.message ??
        "Something went wrong, Please try again or contact support if the issue persists.";
      loginData.value.otp = undefined;
      return;
    }
    const {
      apiKey,
      userId,
      companyId,
      token: firebaseToken,
      jwt,
      refreshJwt,
      authToken,
      refreshToken,
    } = res;
    const _a = btoa(
      JSON.stringify({
        apiKey,
        userId,
        companyId,
      })
    );
    document.cookie = `a=${_a}; path=/`;
    localStorage.setItem(
      "a",
      `"${btoa(
        JSON.stringify({
          firebaseToken,
          apiKey,
          userId,
          companyId,
          jwt,
          refreshJwt,
          authToken,
          refreshToken,
        })
      )}"`
    );
    localStorage.setItem("loginDate", `"${new Date().toISOString()}"`);
    await useNuxtApp().$signInWithToken(firebaseToken);
    useRootStore().toggleRootLoading(true);
    const route = useRoute();
    navigateTo(
      `/?login=true${
        route.query.redirect ? `&redirect=${route.query.redirect}` : ""
      }`
    );
  };

  /**
   * @function getAuthToken
   * @description Retrieves the current authentication token
   * @returns {string|undefined} The authentication token if available
   */
  const getAuthToken = (): string | undefined => a.value?.authToken;

  /**
   * @function getRefreshToken
   * @description Retrieves the current refresh token
   * @returns {string|undefined} The refresh token if available
   */
  const getRefreshToken = (): string | undefined => a.value?.refreshToken;

  const getAuthFirebaseToken = () => a.value?.firebaseToken;
  const getJwt = () => a.value?.jwt;
  const getRefreshJwt = () => a.value?.refreshJwt;
  const getCompanyId = () => a.value?.companyId;
  const getUserId = () => a.value?.userId;
  const getApiKey = () => a.value?.apiKey;

  const setAuthLoading = (loading: boolean) => {
    isAuthLoading.value = loading;
  };

  return {
    isAuthLoading,
    setAuthLoading,
    submitLoginForm,
    submitOtp,
    getAuthToken,
    getRefreshToken,
    getAuthFirebaseToken,
    getJwt,
    getRefreshJwt,
    getCompanyId,
    getUserId,
    getApiKey,
    fbUser,
    currentPage,
    isLoading,
    loginError,
    loginData,
    setAuth,
    getAuthStatus,
  };
});

if (import.meta.hot) {
  import.meta.hot.accept(acceptHMRUpdate(useAuthStore, import.meta.hot));
}

/**
 * @interface LoginResponse
 * @description Response structure for the initial login attempt
 * @property {boolean} error - Indicates if the login attempt encountered an error
 * @property {string} [message] - Optional error message or success message
 * @property {string} [token] - Optional authentication token returned on successful login
 */
export interface LoginResponse {
  error: boolean;
  message?: string;
  token?: string;
}

/**
 * @interface OtpResponse
 * @description Comprehensive response structure after successful OTP verification
 * @property {boolean} error - Indicates if the OTP verification encountered an error
 * @property {string} [message] - Optional error or success message
 * @property {number} status - HTTP status code of the response
 * @property {string} token - Authentication token for Firebase
 * @property {string} apiKey - API key for subsequent requests
 * @property {string} userId - Unique identifier for the authenticated user
 * @property {string} companyId - Unique identifier for the user's company
 * @property {string} role - User's role in the system
 * @property {string} type - User type classification
 * @property {string} name - User's display name
 * @property {Object} permissions - User's permission settings
 * @property {boolean} permissions.workflowsEnabled - Whether workflows are enabled for the user
 * @property {boolean} permissions.workflowsReadOnly - Whether workflow access is read-only
 * @property {string} authToken - Primary authentication token
 * @property {string} refreshToken - Token used to refresh the authToken
 * @property {string} jwt - JSON Web Token for authentication
 * @property {string} refreshJwt - Token used to refresh the JWT
 */
export interface OtpResponse {
  error: boolean;
  message?: string;
  status: number;
  token: string;
  apiKey: string;
  userId: string;
  companyId: string;
  role: string;
  type: string;
  name: string;
  permissions: {
    workflowsEnabled: boolean;
    workflowsReadOnly: boolean;
  };
  authToken: string;
  refreshToken: string;
  jwt: string;
  refreshJwt: string;
}
